<?php

namespace App\Http\Controllers\Staff;

use App\Http\Controllers\Controller;
use App\Http\Services\DocumentService;
use App\Http\Services\WarehouseCounterService;
use App\Mail\StockInOutEmail;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Warehouse;
use App\Models\WarehouseCustomer;
use App\Models\WarehouseCustomerProduct;
use App\Models\WarehouseDocument;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Yajra\DataTables\DataTables;

class OrderStockOutController extends Controller
{
    public function index(Request $request, Warehouse $warehouse)
    {
        $status = $request->query('status', 'pending');

        return view('staff.order.stock-out.index', compact('status', 'warehouse'));
    }

    public function query(Request $request, Warehouse $warehouse)
    {
        $status = $request->input('status');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $query = $warehouse->orders()
            ->whereIn('process_type_key', [Order::PROCESS_TYPE_PICKING, Order::PROCESS_TYPE_COLLECT])
            ->select('orders.*');

        if ($status == 'pending') {
            $query->where('status_key', Order::STATUS_PENDING_APPROVAL);
        } elseif ($status == 'outbound') {
            $query->where('status_key', Order::STATUS_PENDING_PROCESS);
        } elseif ($status == 'completed') {
            $query->where('status_key', Order::STATUS_COMPLETED);
        } elseif ($status == 'cancelled') {
            $query->where('status_key', Order::STATUS_CANCELLED);
        }

        if (!empty($startDate)) {
            $startDate = Carbon::parse($startDate)->startOfDay();  // Set start of the day
            $query->where('request_date', '>=', $startDate);
        }

        if (!empty($endDate)) {
            $endDate = Carbon::parse($endDate)->endOfDay();  // Set end of the day
            $query->where('request_date', '<=', $endDate);
        }

        $result = DataTables::of($query)->addColumn('request_date', function ($row) {
            return $row->request_date ? $row->request_date->format('Y-m-d') : '-';
        })->addColumn('company_name', function ($row) {
            return $row->warehouseCustomer->name ?? '-';
        })->addColumn('est_delivery_date', function ($row) {
            return $row->detail->est_delivery_date ?? "-";
        })->addColumn('total_sku', function ($row) {
            return $row->items()->count();
        })->addColumn('total_quantity', function ($row) {
            return $row->items()->sum('quantity');
        })->addColumn('process_type_text', function ($row) {
            $processTypeLabels = [
                Order::PROCESS_TYPE_PICKING => '<span class="badge badge-warning">Picking</span>',
                Order::PROCESS_TYPE_COLLECT => '<span class="badge badge-success">Collect</span>',
            ];

            return $processTypeLabels[$row->process_type_key] ?? '<span class="badge badge-secondary">Unknown</span>';
        })->addColumn('status_text', function ($row) {
            $statusLabels = [
                Order::STATUS_PENDING_APPROVAL => '<span class="badge badge-warning">Pending Approval</span>',
                Order::STATUS_PENDING_PROCESS => '<span class="badge badge-info">Pending Process</span>',
                Order::STATUS_COMPLETED => '<span class="badge badge-success">Completed</span>',
                Order::STATUS_REJECTED => '<span class="badge badge-danger">Rejected</span>',
                Order::STATUS_CANCELLED => '<span class="badge badge-secondary">Cancelled</span>',
            ];

            return $statusLabels[$row->status_key] ?? '<span class="badge badge-dark">Unknown</span>';
        })->addColumn('actions', function ($row) use ($warehouse) {
            $actions = '<a href="' . route('staff.stock-out.show', [$warehouse, $row->id]) . '" class="btn btn-sm btn-info ms-2 mb-2">View</a>';

            // Add edit button if the order is in pending approval or pending process status
            // AND the process type is not COLLECT
            if (in_array($row->status_key, [Order::STATUS_PENDING_APPROVAL, Order::STATUS_PENDING_PROCESS]) &&
                $row->process_type_key !== Order::PROCESS_TYPE_COLLECT) {
                $actions .= '<a href="' . route('staff.stock-out.edit', [$warehouse, $row->id]) . '" class="btn btn-sm btn-success ms-2 mb-2">Edit</a>';
            }

            // Add QR button
            $actions .= '<button type="button" class="btn btn-sm btn-warning ms-2 mb-2 qr-btn" data-order-id="' . $row->id . '" data-order-num="' . $row->order_num . '" data-bs-toggle="modal" data-bs-target="#qrModal">QR</button>';

            return $actions;
        })->addColumn('created_by', function ($row) {
            return $row->createdBy ? $row->createdBy->name : 'Folk Admin';
        })->editColumn('created_at', function ($row) {
            return $row->created_at ? $row->created_at->format('Y-m-d H:i:s') : '-';
        })->rawColumns(['process_type_text', 'status_text', 'actions'])->make(true);

        return $result;
    }

    public function create(Warehouse $warehouse)
    {
        return view('staff.order.stock-out.create', compact('warehouse'));
    }

    public function store(Request $request, Warehouse $warehouse)
    {
        $rules = [
            'est_delivery_date' => 'required|date',
            'warehouse_customer_id' => 'required|exists:warehouse_customer,id',
            'products.*' => 'required|array',
            'products.*.id' => 'required|exists:warehouse_customer_products,id',
            'products.*.stock_id' => 'required|exists:warehouse_rack_product_stocks,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.remarks' => 'nullable|string|max:255',
            'ship_via' => 'required|in:delivery,self_pickup',
        ];

        if ($request->ship_via === 'delivery') {
            $rules = array_merge($rules, [
                'ship_to_name' => 'required|string|max:255',
                'ship_to_address1' => 'required|string|max:255',
                'ship_to_address2' => 'nullable|string|max:255',
                'ship_to_country' => 'required|string|max:255',
                'ship_to_state' => 'required|string|max:255',
                'ship_to_city' => 'required|string|max:255',
                'ship_to_postcode' => 'required|string|max:20',
            ]);
        } elseif ($request->ship_via === 'self_pickup') {
            $rules = array_merge($rules, [
                'logistic_company_name' => 'required|string|max:255',
                'driver_name' => 'nullable|string|max:255',
                'car_plate_number' => 'nullable|string|max:255',
                'driver_phone_e164' => 'nullable|string|max:255',
            ]);
        }

        $validatedData = $request->validate($rules);

        $order = new Order;
        $order->request_date = now();
        $order->warehouse_id = $warehouse->id;
        $order->warehouse_customer_id = $validatedData['warehouse_customer_id'];
        $order->order_type_key = Order::ORDER_TYPE_STOCK_OUT;
        $order->process_type_key = 3;
        $order->status_key = 2;
        $order->created_by_id = Auth::id();
        $order->save();

        //*Auto assign order number
        WarehouseCounterService::assignOrderNum($order);

        // Handle 'N/A' expiry date by converting it to null
        $expiryDate = isset($request->expiry_date) && $request->expiry_date !== 'N/A' ? $request->expiry_date : null;

        $order->detail()->create([
            'est_delivery_date' => $validatedData['est_delivery_date'],
            'ship_to_name' => $request->ship_via === 'delivery' ? $validatedData['ship_to_name'] : null,
            'ship_to_address1' => $request->ship_via === 'delivery' ? $validatedData['ship_to_address1'] : null,
            'ship_to_address2' => $request->ship_via === 'delivery' ? $validatedData['ship_to_address2'] : null,
            'ship_to_country' => $request->ship_via === 'delivery' ? $validatedData['ship_to_country'] : null,
            'ship_to_state' => $request->ship_via === 'delivery' ? $validatedData['ship_to_state'] : null,
            'ship_to_city' => $request->ship_via === 'delivery' ? $validatedData['ship_to_city'] : null,
            'ship_to_postcode' => $request->ship_via === 'delivery' ? $validatedData['ship_to_postcode'] : null,
            'logistic_company_name' => $request->ship_via === 'self_pickup' ? $validatedData['logistic_company_name'] : null,
            'driver_name' => $request->ship_via === 'self_pickup' ? $validatedData['driver_name'] : null,
            'car_plate_number' => $request->ship_via === 'self_pickup' ? $validatedData['car_plate_number'] : null,
            'driver_phone_e164' => $request->ship_via === 'self_pickup' ? $validatedData['driver_phone_e164'] : null,
            'expiry_date' => $expiryDate,
        ]);

        foreach ($request->products as $product) {
            // Handle 'N/A' expiry date by converting it to null
            $expiryDate = isset($product['expiry_date']) && $product['expiry_date'] !== 'N/A' ? $product['expiry_date'] : null;

            $order->items()->create([
                'warehouse_customer_product_id' => $product['id'],
                'warehouse_rack_product_stock_id' => $product['stock_id'],
                'quantity' => $product['quantity'],
                'remarks' => $product['remarks'],
                'expiry_date' => $expiryDate,
                'serial_number' => $product['serial_number'] ?? null,
            ]);
        }

        Session::flash('alert-success', 'Order created successfully!');

        return redirect()->route('staff.stock-out', ['status' => 'outbound', 'warehouse' => $warehouse]);
    }

    public function show(Warehouse $warehouse, Order $order)
    {
        return view('staff.order.stock-out.show', compact('warehouse', 'order'));
    }

    public function edit(Warehouse $warehouse, Order $order)
    {
        // Check if the order is in a status that allows editing
        if (!in_array($order->status_key, [Order::STATUS_PENDING_APPROVAL, Order::STATUS_PENDING_PROCESS])) {
            Session::flash('alert-danger', 'This order cannot be edited because it is not in pending approval or pending process status.');
            return redirect()->route('staff.stock-out.show', [$warehouse, $order]);
        }

        // Check if the order has COLLECT process type
        if ($order->process_type_key === Order::PROCESS_TYPE_COLLECT) {
            Session::flash('alert-danger', 'Collect orders cannot be edited.');
            return redirect()->route('staff.stock-out.show', [$warehouse, $order]);
        }

        $warehouseCustomers = WarehouseCustomer::where('warehouse_id', $warehouse->id)
            ->where('is_active', true)
            ->pluck('name', 'id');

        return view('staff.order.stock-out.edit', compact('warehouse', 'order', 'warehouseCustomers'));
    }

    public function update(Request $request, Warehouse $warehouse, Order $order)
    {
        // Check if the order is in a status that allows editing
        if (!in_array($order->status_key, [Order::STATUS_PENDING_APPROVAL, Order::STATUS_PENDING_PROCESS])) {
            Session::flash('alert-danger', 'This order cannot be updated because it is not in pending approval or pending process status.');
            return redirect()->route('staff.stock-out.show', [$warehouse, $order]);
        }

        // Check if the order has COLLECT process type
        if ($order->process_type_key === Order::PROCESS_TYPE_COLLECT) {
            Session::flash('alert-danger', 'Collect orders cannot be updated.');
            return redirect()->route('staff.stock-out.show', [$warehouse, $order]);
        }



        $rules = [
            'est_delivery_date' => 'required|date',
            'warehouse_customer_id' => 'required|exists:warehouse_customer,id',
            'products.*' => 'required|array',
            'products.*.id' => 'required|exists:warehouse_customer_products,id',
            'products.*.stock_id' => 'nullable|exists:warehouse_rack_product_stocks,id', // Changed from required to nullable
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.remarks' => 'nullable|string|max:255',
            'products.*.expiry_date' => 'nullable|date',
            'products.*.serial_number' => 'nullable|string|max:255', // Added expiry_date validation
            'ship_via' => 'required|in:delivery,self_pickup',
        ];

        if ($request->ship_via === 'delivery') {
            $rules = array_merge($rules, [
                'ship_to_name' => 'required|string|max:255',
                'ship_to_address1' => 'required|string|max:255',
                'ship_to_address2' => 'nullable|string|max:255',
                'ship_to_country' => 'required|string|max:255',
                'ship_to_state' => 'required|string|max:255',
                'ship_to_city' => 'required|string|max:255',
                'ship_to_postcode' => 'required|string|max:20',
            ]);
        } elseif ($request->ship_via === 'self_pickup') {
            $rules = array_merge($rules, [
                'logistic_company_name' => 'required|string|max:255',
                'driver_name' => 'nullable|string|max:255',
                'car_plate_number' => 'nullable|string|max:255',
                'driver_phone_e164' => 'nullable|string|max:255',
            ]);
        }

        $validatedData = $request->validate($rules);

        try {
            // Update order details
            $order->warehouse_customer_id = $validatedData['warehouse_customer_id'];
            $order->save();

            // Update order detail
            $order->detail()->update([
                'est_delivery_date' => $validatedData['est_delivery_date'],
                'ship_to_name' => $request->ship_via === 'delivery' ? $validatedData['ship_to_name'] : null,
                'ship_to_address1' => $request->ship_via === 'delivery' ? $validatedData['ship_to_address1'] : null,
                'ship_to_address2' => $request->ship_via === 'delivery' ? $validatedData['ship_to_address2'] : null,
                'ship_to_country' => $request->ship_via === 'delivery' ? $validatedData['ship_to_country'] : null,
                'ship_to_state' => $request->ship_via === 'delivery' ? $validatedData['ship_to_state'] : null,
                'ship_to_city' => $request->ship_via === 'delivery' ? $validatedData['ship_to_city'] : null,
                'ship_to_postcode' => $request->ship_via === 'delivery' ? $validatedData['ship_to_postcode'] : null,
                'logistic_company_name' => $request->ship_via === 'self_pickup' ? $validatedData['logistic_company_name'] : null,
                'driver_name' => $request->ship_via === 'self_pickup' ? $validatedData['driver_name'] : null,
                'car_plate_number' => $request->ship_via === 'self_pickup' ? $validatedData['car_plate_number'] : null,
                'driver_phone_e164' => $request->ship_via === 'self_pickup' ? $validatedData['driver_phone_e164'] : null,
            ]);

            // Delete existing items with their related records
            foreach ($order->items as $item) {
                try {
                    // First delete related QC files
                    foreach ($item->qcResults as $qcResult) {
                        $qcResult->qcFiles()->delete();
                    }

                    // Then delete related QC results
                    $item->qcResults()->delete();

                    // Delete any stock flow items if they exist
                    if (method_exists($item, 'stockFlowItems')) {
                        $item->stockFlowItems()->delete();
                    }

                    // Then delete the item itself
                    $item->delete();
                } catch (\Exception $e) {
                    \Log::error('Error deleting order item: ' . $e->getMessage());
                    throw $e;
                }
            }

            // Create new items
            foreach ($validatedData['products'] as $product) {
                // Handle 'N/A' expiry date by converting it to null
                $expiryDate = isset($product['expiry_date']) && $product['expiry_date'] !== 'N/A' && $product['expiry_date'] !== '' ? $product['expiry_date'] : null;

                // Create item with stock_id if available
                $itemData = [
                    'warehouse_customer_product_id' => $product['id'],
                    'quantity' => $product['quantity'],
                    'remarks' => $product['remarks'] ?? null,
                    'expiry_date' => $expiryDate,
                    'sequence' => $product['sequence'] ?? null, // Added sequence
                ];

                // Only add stock_id if it's not null
                if (!empty($product['stock_id'])) {
                    $itemData['warehouse_rack_product_stock_id'] = $product['stock_id'];
                }

                $order->items()->create($itemData);
            }

            Session::flash('alert-success', 'Order updated successfully!');
        } catch (\Exception $e) {
            \Log::error('Error updating order: ' . $e->getMessage());
            Session::flash('alert-danger', 'An error occurred while updating the order. Please try again or contact support.');
        }

        return redirect()->route('staff.stock-out.show', [$warehouse, $order]);
    }

    public function getWarehouseCustomerProducts(Warehouse $warehouse, $warehouseCustomerId)
    {
        // Get products for this warehouse and customer
        $products = WarehouseCustomerProduct::where('warehouse_customer_id', $warehouseCustomerId)
            ->with(['productStocks' => function($query) use ($warehouse) {
                // Only get stocks from this warehouse
                $query->whereHas('rack', function($q) use ($warehouse) {
                    $q->where('warehouse_id', $warehouse->id);
                })->with('rack');
            }, 'uomType'])
            ->get()
            ->flatMap(function ($product) {
                return $product->productStocks
                    ->filter(function ($stock) {
                        // Only include stocks with quantity > 0
                        return $stock->quantity > 0;
                    })
                    ->map(function ($stock) use ($product) {
                        return [
                            'id' => $product->id,
                            'sku' => $product->sku,
                            'name' => $product->name,
                            'image' => $product->image,
                            'cubage' => $product->cubage ?? '-',
                            'weight' => $product->weight ?? '-',
                            'uom' => $product->uomType->name ?? '-',
                            'quantity' => $stock->quantity,
                            'expiry_date' => $stock->expiry_date ? Carbon::parse($stock->expiry_date)->format('Y-m-d') : null,
                            'serial_number' => $stock->serial_number ?? '',
                            'rack_name' => $stock->rack->display_name ?? 'N/A',
                            'rack_id' => $stock->rack->id ?? null,
                            'stock_id' => $stock->id,
                            'remarks' => $stock->remarks ?? '-',
                        ];
                    });
            });

        return Datatables::of($products)->make(true);
    }

    public function updateStatus(Request $request, Warehouse $warehouse, Order $order)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:1,2,3,4,5',
            'reason' => 'required_if:status,4|max:2500',
        ], [
            'status.required' => 'The status field is required.',
            'status.in' => 'The selected status is invalid.',
            'reason.required' => 'The reason field is required.',
            'reason.max' => 'The reason may not be greater than 2500 characters.',
        ]);

        if ($validator->fails()) {
            Session::flash('alert-danger', 'Please check the form for errors and try again.');

            return redirect()->route('staff.stock-out.show', [$warehouse, $order]);
        }

        $user = $request->user();

        $status = (int) $request->input('status');

        if ($status === Order::STATUS_COMPLETED && $order->status !== Order::STATUS_PENDING_PROCESS) {
            Session::flash('alert-danger', 'Order must be in pending process to be completed.');

            return redirect()->route('staff.stock-out.show', [$warehouse, $order]);
        }

        $order->status_key = $status;

        /***** Send email notifcation to user *****/
        $sender = config('mail.from.address');
        $customer = WarehouseCustomer::find($order->warehouse_customer_id);
        $user = $customer->customer;
        $warehouse = Warehouse::find($order->warehouse_id);
        $company = $order->warehouse->company;

        $info = [
            'title' => ' [FOLK APP] Stock Out Order at ' . ($company ? $company->name : ''),
            'url_text' => 'Outbound',
            'url' => 'https://' . config('app.url') . '/customer/' . $warehouse->slug . '/stock-out',
            'type' => 'Stock Out',
        ];

        $title = $info['title'];
        $receiver = $user->email;

        switch ($status) {
            case Order::STATUS_REJECTED:
                $order->rejected_reason = $request->reason;
                $order->rejectable()->associate($user);
                Session::flash('alert-success', 'Order has been rejected.');
                break;
            case Order::STATUS_CANCELLED:
                $order->completed_at = now();
                Session::flash('alert-success', 'Order has been cancelled.');
                break;
            case Order::STATUS_PENDING_PROCESS:
                $order->approvable()->associate($user);

                $this->sendEmail($sender, $receiver, $info, $title);
                Session::flash('alert-success', 'Order is now pending process.');
                break;
        }

        $order->save();

        return redirect()->route('staff.stock-out.show', [$warehouse, $order]);
    }

    public function sendEmail($sender, $receiver, $info, $subject)
    {
        Mail::send(new StockInOutEmail($sender, $receiver, $info, $subject));
    }

    public function generateDeliveryOrder(Warehouse $warehouse, Order $order)
    {
        // Check if the order already has a delivery order document
        $existingDocument = WarehouseDocument::where('referable_type', Order::class)
            ->where('referable_id', $order->id)
            ->where('type_key', WarehouseDocument::TYPE_DELIVERY_ORDER)
            ->first();

        if ($existingDocument) {
            // If a delivery order already exists, redirect to it
            Session::flash('alert-info', 'Delivery order already exists for this order.');
            return redirect()->route('staff.document', ['warehouse' => $warehouse, 'type' => 'do']);
        }

        // Generate a new delivery order
        DocumentService::generateDeliveryOrder($order);

        Session::flash('alert-success', 'Delivery order generated successfully!');
        return redirect()->route('staff.document', ['warehouse' => $warehouse, 'type' => 'do']);
    }

    public function downloadQrCode(Warehouse $warehouse, Order $order, $type)
    {
        if (!in_array($type, ['image', 'pdf'])) {
            abort(404);
        }

        $qrData = $order->id;
        $logoPath = public_path('dashboard-assets/media/logos/logo.png');

        if ($type === 'image') {
            return $this->generateQrImage($qrData, $order, $logoPath);
        } else {
            return $this->generateQrPdf($qrData, $order, $logoPath);
        }
    }

    private function generateQrImage($qrData, $order, $logoPath)
    {
        // Create QR code with logo using external API and overlay
        $qrSize = 400;
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$qrSize}x{$qrSize}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($qrSize * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        // Generate filename
        $filename = "order-{$order->order_num}-qr-code.png";

        // Return image response
        return response($qrImage->toPng())
            ->header('Content-Type', 'image/png')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    private function generateQrPdf($qrData, $order, $logoPath)
    {
        // Generate QR code with logo
        $qrSize = 300;
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$qrSize}x{$qrSize}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($qrSize * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        // Convert to base64 for PDF
        $qrImageData = 'data:image/png;base64,' . base64_encode($qrImage->toPng());

        $data = [
            'order' => $order,
            'qrImageData' => $qrImageData,
            'logoPath' => $logoPath,
        ];

        $pdf = Pdf::loadView('pdf.stock-out-qr-code', $data);
        $filename = "order-{$order->order_num}-qr-code.pdf";

        return $pdf->download($filename);
    }

    public function displayQrImage(Warehouse $warehouse, Order $order, $size = 300)
    {
        $qrData = $order->id;
        $logoPath = public_path('dashboard-assets/media/logos/logo.png');

        // Validate size
        $size = intval($size);
        if ($size < 50 || $size > 1000) {
            $size = 300;
        }

        // Create QR code using external API
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$size}x{$size}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($size * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        // Return image response for display
        return response($qrImage->toPng())
            ->header('Content-Type', 'image/png')
            ->header('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    }

    public function downloadItemQrCode(Warehouse $warehouse, Order $order, OrderItem $item)
    {
        $qrData = $item->id;
        $logoPath = public_path('dashboard-assets/media/logos/logo.png');

        return $this->generateItemQrImage($qrData, $item, $logoPath);
    }

    private function generateItemQrImage($qrData, $item, $logoPath)
    {
        // Create QR code with logo using external API and overlay
        $qrSize = 400;
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$qrSize}x{$qrSize}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($qrSize * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        // Generate filename
        $filename = "item-{$item->id}-{$item->warehouseCustomerProduct->sku}-qr-code.png";

        // Return image response
        return response($qrImage->toPng())
            ->header('Content-Type', 'image/png')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    public function displayItemQrImage(Warehouse $warehouse, Order $order, OrderItem $item, $size = 300)
    {
        $qrData = $item->id;
        $logoPath = public_path('dashboard-assets/media/logos/logo.png');

        // Validate size
        $size = intval($size);
        if ($size < 50 || $size > 1000) {
            $size = 300;
        }

        // Create QR code using external API
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$size}x{$size}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($size * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        return response($qrImage->toPng())
            ->header('Content-Type', 'image/png');
    }
}
