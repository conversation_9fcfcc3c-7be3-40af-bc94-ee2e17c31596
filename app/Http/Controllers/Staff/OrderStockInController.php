<?php

namespace App\Http\Controllers\Staff;

use App\Http\Services\SendNotificationService;
use App\Http\Services\WarehouseCounterService;
use App\Mail\StockInOutEmail;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Warehouse;
use App\Models\WarehouseCustomer;
use App\Models\WarehouseCustomerProduct;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Yajra\DataTables\DataTables;

class OrderStockInController extends Controller
{
    public function index(Request $request, Warehouse $warehouse)
    {
        $status = $request->query('status', 'pending');

        return view('staff.order.stock-in.index', compact('status', 'warehouse'));
    }

    public function query(Request $request, Warehouse $warehouse)
    {
        $status = $request->input('status');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $query = $warehouse->orders()
            ->whereIn('process_type_key', [Order::PROCESS_TYPE_CHECKING, Order::PROCESS_TYPE_PUT_AWAY])
            ->select('orders.*');

        if ($status == 'pending') {
            $query->where('status_key', Order::STATUS_PENDING_APPROVAL);
        } elseif ($status == 'inbound') {
            $query->where('status_key', Order::STATUS_PENDING_PROCESS);
        } elseif ($status == 'completed') {
            $query->where('status_key', Order::STATUS_COMPLETED);
        } elseif ($status == 'cancelled') {
            $query->where('status_key', Order::STATUS_CANCELLED);
        }

        if (!empty($startDate)) {
            $startDate = Carbon::parse($startDate)->startOfDay();  // Set start of the day
            $query->where('request_date', '>=', $startDate);
        }

        if (!empty($endDate)) {
            $endDate = Carbon::parse($endDate)->endOfDay();  // Set end of the day
            $query->where('request_date', '<=', $endDate);
        }

        $result = DataTables::of($query)->addColumn('request_date', function ($row) {
            return $row->request_date ? $row->request_date->format('Y-m-d') : '-';
        })->addColumn('company_name', function ($row) {
            return $row->warehouseCustomer->name;
        })->addColumn('est_arrival_date', function ($row) {
            return $row->detail->est_arrival_date ?? '-';
        })->addColumn('total_sku', function ($row) {
            return $row->items()->count();
        })->addColumn('total_quantity', function ($row) {
            return $row->items()->sum('quantity');
        })
            ->addColumn('process_type_text', function ($row) {
                $processTypeLabels = [
                    Order::PROCESS_TYPE_CHECKING => '<span class="badge badge-info">Checking</span>',
                    Order::PROCESS_TYPE_PUT_AWAY => '<span class="badge badge-primary">Put Away</span>',
                ];

                return $processTypeLabels[$row->process_type_key] ?? '<span class="badge badge-secondary">Unknown</span>';
            })->addColumn('status_text', function ($row) {
                $statusLabels = [
                    Order::STATUS_PENDING_APPROVAL => '<span class="badge badge-warning">Pending Approval</span>',
                    Order::STATUS_PENDING_PROCESS => '<span class="badge badge-info">Pending Process</span>',
                    Order::STATUS_COMPLETED => '<span class="badge badge-success">Completed</span>',
                    Order::STATUS_REJECTED => '<span class="badge badge-danger">Rejected</span>',
                    Order::STATUS_CANCELLED => '<span class="badge badge-secondary">Cancelled</span>',
                ];

                return $statusLabels[$row->status_key] ?? '<span class="badge badge-dark">Unknown</span>';
            })->addColumn('created_by', function ($row) {
                return $row->createdBy ? $row->createdBy->name : 'Folk Admin';
            })->editColumn('created_at', function ($row) {
                return $row->created_at ? $row->created_at->format('Y-m-d H:i:s') : '-';
            })->addColumn('actions', function ($row) use ($warehouse) {
                $actions = '<a href="' . route('staff.stock-in.show', [$warehouse, $row->id]) . '" class="btn btn-sm btn-info ms-2 mb-2">View</a>';

                // Add edit button if the order is in pending approval or pending process status
                // AND the process type is not PUT_AWAY
                if (in_array($row->status_key, [Order::STATUS_PENDING_APPROVAL, Order::STATUS_PENDING_PROCESS]) &&
                    $row->process_type_key !== Order::PROCESS_TYPE_PUT_AWAY) {
                    $actions .= '<a href="' . route('staff.stock-in.edit', [$warehouse, $row->id]) . '" class="btn btn-sm btn-success ms-2 mb-2">Edit</a>';
                }

                // Add QR button
                $actions .= '<button type="button" class="btn btn-sm btn-warning ms-2 mb-2 qr-btn" data-order-id="' . $row->id . '" data-order-num="' . $row->order_num . '" data-bs-toggle="modal" data-bs-target="#qrModal">QR</button>';

                return $actions;
            })->rawColumns(['process_type_text', 'status_text', 'actions'])->make(true);

        return $result;
    }

    public function create(Warehouse $warehouse)
    {
        return view('staff.order.stock-in.create', compact('warehouse'));
    }

    public function store(Request $request, Warehouse $warehouse)
    {
        $validatedData = $request->validate([
            'est_arrival_date' => 'required|date',
            'warehouse_customer_id' => 'required|exists:warehouse_customer,id',
            'products.*.id' => 'required|exists:warehouse_customer_products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.expiry_date' => 'nullable|date',
            'products.*.serial_number' => 'nullable|string|max:255',
            'products.*.remarks' => 'nullable|string|max:255',
        ]);

        $order = new Order;
        $order->request_date = now();
        $order->warehouse_id = $warehouse->id;
        $order->warehouse_customer_id = $validatedData['warehouse_customer_id'];
        $order->order_type_key = Order::ORDER_TYPE_STOCK_IN;
        $order->request_date = now();
        $order->process_type_key = 1;
        $order->status_key = 2;
        $order->created_by_id = Auth::id();
        $order->save();

        //*Auto assign order number
        WarehouseCounterService::assignOrderNum($order);

        $order->detail()->create([
            'est_arrival_date' => $validatedData['est_arrival_date'],
        ]);

        foreach ($validatedData['products'] as $product) {
            $order->items()->create([
                'warehouse_customer_product_id' => $product['id'],
                'quantity' => $product['quantity'],
                'expiry_date' => $product['expiry_date'],
                'serial_number' => $product['serial_number'] ?? null,
                'remarks' => $product['remarks'],
            ]);
        }

        Session::flash('alert-success', 'Order created successfully!');

        return redirect()->route('staff.stock-in', ['status' => 'inbound', 'warehouse' => $warehouse]);
    }

    public function show(Warehouse $warehouse, Order $order)
    {
        return view('staff.order.stock-in.show', compact('warehouse', 'order'));
    }

    public function getWarehouseCustomerProducts(Warehouse $warehouse, $warehouseCustomerId)
    {
        $products = WarehouseCustomerProduct::where('warehouse_customer_id', $warehouseCustomerId)
            ->with('productStocks')
            ->get()
            ->map(function ($product) {
                // dd($product);
                $totalQuantity = $product->productStocks->sum('quantity');

                return [
                    'id' => $product->id,
                    'sku' => $product->sku,
                    'name' => $product->name,
                    'image' => $product->image,
                    'cubage' => $product->cubage ?? '-',
                    'weight' => $product->weight ?? '-',
                    'uom' => $product->uomType->name ?? '-',
                    'quantity' => $totalQuantity,
                ];
            });

        return Datatables::of($products)->make(true);
    }

    public function updateStatus(Request $request, Warehouse $warehouse, Order $order)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:1,2,3,4,5',
            'reason' => 'required_if:status,4|max:2500',
        ], [
            'status.required' => 'The status field is required.',
            'status.in' => 'The selected status is invalid.',
            'reason.required' => 'The reason field is required.',
            'reason.max' => 'The reason may not be greater than 2500 characters.',
        ]);

        if ($validator->fails()) {
            Session::flash('alert-danger', 'Please check the form for errors and try again.');

            return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
        }

        $status = (int) $request->input('status');

        if ($status === Order::STATUS_COMPLETED && $order->status !== Order::STATUS_PENDING_PROCESS) {
            Session::flash('alert-danger', 'Order must be in pending process to be completed.');

            return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
        }

        $user = $request->user();

        $status = (int) $request->input('status');

        if ($status === Order::STATUS_COMPLETED && $order->status !== Order::STATUS_PENDING_PROCESS) {
            Session::flash('alert-danger', 'Order must be in pending process to be completed.');

            return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
        }

        $order->status_key = $status;

        /***** Send email notifcation to user *****/
        $sender = config('mail.from.address');
        $customer = WarehouseCustomer::find($order->warehouse_customer_id);
        $user = $customer->customer;
        $warehouse = Warehouse::find($order->warehouse_id);
        $company = $order->warehouse->company;

        $info = [
            'title' => ' [FOLK APP] Stock In Order at '.($company ? $company->name : ''),
            'url_text' => 'Inbound',
            'url' => url('/customer/'.$warehouse->slug. '/stock-in'),
            'type' => 'Stock In',
            'order' => $order,
        ];

        $title = $info['title'];
        $receiver = $user->email;

        /***** Send web notifcation to user *****/
        $stock_in_order_notification = new SendNotificationService;

        switch ($status) {
            case Order::STATUS_REJECTED:
                $order->rejected_reason = $request->reason;
                $order->rejectable()->associate($user);

                $stock_in_order_notification->sendCustomerNotification($user, 'stock-in-cancelled', $order);

                Log::info('email sent........');

                Session::flash('alert-success', 'Order has been rejected.');
                break;
            case Order::STATUS_CANCELLED:
                $order->completed_at = now();

                $stock_in_order_notification->sendCustomerNotification($user, 'stock-in-cancelled', $order);

                Log::info('email sent........');

                Session::flash('alert-success', 'Order has been cancelled.');
                break;
            case Order::STATUS_PENDING_PROCESS:

                $order->approvable()->associate($user);

                $this->sendEmail($sender, $receiver, $info, $title);

                $stock_in_order_notification->sendCustomerNotification($user, 'stock-in-accepted', $order);

                Log::info('email sent........');

                Session::flash('alert-success', 'Order is now pending process.');
                break;
        }

        $order->save();

        return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
    }

    public function sendEmail($sender, $receiver, $info, $subject)
    {
        Mail::send(new StockInOutEmail($sender, $receiver, $info, $subject));
    }

    public function edit(Warehouse $warehouse, Order $order)
    {
        // Check if the order is in a status that allows editing
        if (!in_array($order->status_key, [Order::STATUS_PENDING_APPROVAL, Order::STATUS_PENDING_PROCESS])) {
            Session::flash('alert-danger', 'This order cannot be edited because it is not in pending approval or pending process status.');
            return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
        }

        // Check if the order has PUT_AWAY process type
        if ($order->process_type_key === Order::PROCESS_TYPE_PUT_AWAY) {
            Session::flash('alert-danger', 'Put Away orders cannot be edited.');
            return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
        }

        $warehouseCustomers = WarehouseCustomer::where('warehouse_id', $warehouse->id)
            ->where('is_active', true)
            ->pluck('name', 'id');

        return view('staff.order.stock-in.edit', compact('warehouse', 'order', 'warehouseCustomers'));
    }

    public function update(Request $request, Warehouse $warehouse, Order $order)
    {
        // Check if the order is in a status that allows editing
        if (!in_array($order->status_key, [Order::STATUS_PENDING_APPROVAL, Order::STATUS_PENDING_PROCESS])) {
            Session::flash('alert-danger', 'This order cannot be updated because it is not in pending approval or pending process status.');
            return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
        }

        // Check if the order has PUT_AWAY process type
        if ($order->process_type_key === Order::PROCESS_TYPE_PUT_AWAY) {
            Session::flash('alert-danger', 'Put Away orders cannot be updated.');
            return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
        }

        $validatedData = $request->validate([
            'est_arrival_date' => 'required|date',
            'warehouse_customer_id' => 'required|exists:warehouse_customer,id',
            'products.*.id' => 'required|exists:warehouse_customer_products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.expiry_date' => 'nullable|date',
            'products.*.serial_number' => 'nullable|string|max:255',
            'products.*.remarks' => 'nullable|string|max:255',
        ]);

        try {
            // Update order details
            $order->warehouse_customer_id = $validatedData['warehouse_customer_id'];
            $order->save();

            // Update order detail
            $order->detail()->update([
                'est_arrival_date' => $validatedData['est_arrival_date'],
            ]);

            // Delete existing items with their related records
            foreach ($order->items as $item) {
                try {
                    // First delete related QC files
                    foreach ($item->qcResults as $qcResult) {
                        $qcResult->files()->delete();
                    }

                    // Then delete related QC results
                    $item->qcResults()->delete();

                    // Delete any stock flow items if they exist
                    if (method_exists($item, 'stockFlowItems')) {
                        $item->stockFlowItems()->delete();
                    }

                    // Then delete the item itself
                    $item->delete();
                } catch (\Exception $e) {
                    \Log::error('Error deleting order item: ' . $e->getMessage());
                    throw $e;
                }
            }

            // Create new items
            foreach ($validatedData['products'] as $product) {
                $order->items()->create([
                    'warehouse_customer_product_id' => $product['id'],
                    'quantity' => $product['quantity'],
                    'expiry_date' => $product['expiry_date'],
                    'serial_number' => $product['serial_number'] ?? null,
                    'remarks' => $product['remarks'],
                ]);

            }

            Session::flash('alert-success', 'Order updated successfully!');
        } catch (\Exception $e) {
            \Log::error('Error updating order: ' . $e->getMessage());
            Session::flash('alert-danger', 'An error occurred while updating the order. Please try again or contact support.');
        }

        return redirect()->route('staff.stock-in.show', [$warehouse, $order]);
    }

    public function downloadQrCode(Warehouse $warehouse, Order $order, $type)
    {
        if (!in_array($type, ['image', 'pdf'])) {
            abort(404);
        }

        $qrData = $order->id;
        $logoPath = public_path('dashboard-assets/media/logos/logo.png');

        if ($type === 'image') {
            return $this->generateQrImage($qrData, $order, $logoPath);
        } else {
            return $this->generateQrPdf($qrData, $order, $logoPath);
        }
    }

    private function generateQrImage($qrData, $order, $logoPath)
    {
        // Create QR code with logo using external API and overlay
        $qrSize = 400;
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$qrSize}x{$qrSize}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($qrSize * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        // Generate filename
        $filename = "order-{$order->order_num}-qr-code.png";

        // Return image response
        return response($qrImage->toPng())
            ->header('Content-Type', 'image/png')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    private function generateQrPdf($qrData, $order, $logoPath)
    {
        // Generate QR code with logo
        $qrSize = 300;
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$qrSize}x{$qrSize}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($qrSize * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        // Convert to base64 for PDF
        $qrImageData = 'data:image/png;base64,' . base64_encode($qrImage->toPng());

        $data = [
            'order' => $order,
            'qrImageData' => $qrImageData,
            'logoPath' => $logoPath,
        ];

        $pdf = Pdf::loadView('pdf.stock-in-qr-code', $data);
        $filename = "order-{$order->order_num}-qr-code.pdf";

        return $pdf->download($filename);
    }

    public function displayQrImage(Warehouse $warehouse, Order $order, $size = 300)
    {
        $qrData = $order->id;
        $logoPath = public_path('dashboard-assets/media/logos/logo.png');

        // Validate size
        $size = intval($size);
        if ($size < 50 || $size > 1000) {
            $size = 300;
        }

        // Create QR code using external API
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$size}x{$size}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($size * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        // Return image response for display
        return response($qrImage->toPng())
            ->header('Content-Type', 'image/png')
            ->header('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    }

    public function downloadItemQrCode(Warehouse $warehouse, Order $order, OrderItem $item)
    {
        $qrData = $item->id;
        $logoPath = public_path('dashboard-assets/media/logos/logo.png');

        return $this->generateItemQrImage($qrData, $item, $logoPath);
    }

    private function generateItemQrImage($qrData, $item, $logoPath)
    {
        // Create QR code with logo using external API and overlay
        $qrSize = 400;
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$qrSize}x{$qrSize}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($qrSize * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        // Generate filename
        $filename = "item-{$item->id}-{$item->warehouseCustomerProduct->sku}-qr-code.png";

        // Return image response
        return response($qrImage->toPng())
            ->header('Content-Type', 'image/png')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    public function displayItemQrImage(Warehouse $warehouse, Order $order, OrderItem $item, $size = 300)
    {
        $qrData = $item->id;
        $logoPath = public_path('dashboard-assets/media/logos/logo.png');

        // Validate size
        $size = intval($size);
        if ($size < 50 || $size > 1000) {
            $size = 300;
        }

        // Create QR code using external API
        $qrUrl = "http://api.qrserver.com/v1/create-qr-code/?data={$qrData}&size={$size}x{$size}&format=png";

        // Get QR code image
        $qrImageContent = file_get_contents($qrUrl);

        if (!$qrImageContent) {
            abort(500, 'Failed to generate QR code');
        }

        // Create image manager
        $manager = new ImageManager(new Driver());

        // Create QR code image from content
        $qrImage = $manager->read($qrImageContent);

        // If logo exists, overlay it on the QR code
        if (file_exists($logoPath)) {
            $logo = $manager->read($logoPath);

            // Resize logo to fit in center of QR code (about 20% of QR size)
            $logoSize = intval($size * 0.2);
            $logo->resize($logoSize, $logoSize);

            // Place logo on QR code center
            $qrImage->place($logo, 'center');
        }

        return response($qrImage->toPng())
            ->header('Content-Type', 'image/png');
    }
}
