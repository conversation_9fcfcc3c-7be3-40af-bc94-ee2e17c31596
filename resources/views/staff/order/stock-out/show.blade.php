@extends('staff.layouts.index')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar pt-10 mb-0">
            <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex align-items-stretch">
                <div class="app-toolbar-wrapper d-flex flex-stack flex-wrap gap-4 w-100">
                    <div class="page-title d-flex flex-column justify-content-center gap-1 me-3">
                        <h1 class="page-heading d-flex flex-column justify-content-center text-gray-900 fw-bold fs-3 m-0">{{ $order->name }} </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('staff.switch') }}" class="text-hover-primary">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('staff.stock-out', $warehouse) }}" class="text-hover-primary">Stock Out</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                {{ $order->order_num }}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                @include('staff.order.stock-out._menu')
                <div class="card mb-5 mb-xl-10" id="kt_profile_details_view">
                    <div class="card-header cursor-pointer">
                        <div class="card-title m-0">
                            <h3 class="fw-bold m-0">Selected SKUs</h3>
                        </div>
                    </div>
                    <div class="card-body p-9">
                        <table class="table table-bordered" id="selected-products-table">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>SKU Code</th>
                                    <th>SKU Name</th>
                                    <th>Cubage (m³)</th>
                                    <th>Weight (kg)</th>
                                    <th>UOM</th>
                                    <th>Quantity</th>
                                    <th>Expiry Date</th>
                                    <th>Serial Number</th>
                                    <th>Remarks</th>
                                    <th>QR Code</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($order->items as $item)
                                    <tr>
                                        <td><img src="{{ $item->warehouseCustomerProduct->image }}" alt="{{ $item->warehouseCustomerProduct->name }}" width="50"></td>
                                        <td>{{ $item->warehouseCustomerProduct->sku }}</td>
                                        <td>{{ $item->warehouseCustomerProduct->name }}</td>
                                        <td>{{ $item->warehouseCustomerProduct->cubage ?? '-' }}</td>
                                        <td>{{ $item->warehouseCustomerProduct->weight ?? '-' }}</td>
                                        <td>{{ $item->warehouseCustomerProduct->uomType->name ?? '-' }}</td>
                                        <td>{{ $item->quantity }}</td>
                                        <td>{{ $item->expiry_date ?? '-' }}</td>
                                        <td>{{ $item->serial_number ?? '-' }}</td>
                                        <td>{{ $item->remarks ?? '-' }}</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-warning item-qr-btn"
                                                data-item-id="{{ $item->id }}"
                                                data-item-sku="{{ $item->warehouseCustomerProduct->sku }}"
                                                data-bs-toggle="modal"
                                                data-bs-target="#itemQrModal">
                                                <i class="ki-duotone ki-qr-code fs-3">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                </i>
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Item QR Code Modal -->
    <div class="modal fade" id="itemQrModal" tabindex="-1" aria-labelledby="itemQrModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="itemQrModalLabel">SKU QR Code</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="qr-code-large mb-4">
                        <img id="itemQrCodeImage" src="" alt="SKU QR Code" class="border rounded">
                    </div>
                    <div class="qr-info">
                        <p class="mb-2"><strong>Item ID:</strong> <span id="qrItemId"></span></p>
                        <p class="mb-2"><strong>SKU:</strong> <span id="qrItemSku"></span></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <a id="downloadItemQrBtn" href="#" class="btn btn-primary" download>
                        <i class="ki-duotone ki-down fs-3">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                        Download
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Handle Item QR button clicks
    $(document).on('click', '.item-qr-btn', function() {
        const itemId = $(this).data('item-id');
        const itemSku = $(this).data('item-sku');

        // Update modal content
        $('#qrItemId').text(itemId);
        $('#qrItemSku').text(itemSku);

        // Update QR code image
        const qrImageUrl = "{{ route('staff.stock-out.item-qr-image', ['warehouse' => $warehouse, 'order' => $order, 'item' => ':itemId', 'size' => 300]) }}".replace(':itemId', itemId);
        $('#itemQrCodeImage').attr('src', qrImageUrl);

        // Update download link
        const downloadUrl = "{{ route('staff.stock-out.item-qr-download', ['warehouse' => $warehouse, 'order' => $order, 'item' => ':itemId']) }}".replace(':itemId', itemId);
        $('#downloadItemQrBtn').attr('href', downloadUrl);
    });
</script>
@endpush
